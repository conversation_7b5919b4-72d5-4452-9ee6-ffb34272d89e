#!/usr/bin/env python3
"""
Simplified Neo4j AuraDB management helper for Terraform.
Handles plan, apply, and read operations in a single focused script.
"""

import json
import sys
import os
import hashlib
import urllib.request
import urllib.parse
import urllib.error


def do_plan(inputs) -> dict:
    """Get existing instances and determine what needs to be created."""
    api_base_url = inputs["api_base_url"]
    access_token = inputs["access_token"]
    tenant_id = inputs["tenant_id"]
    neo4j_customers = json.loads(inputs["neo4j_customers"])

    # Get existing instances
    url = f"{api_base_url}/instances?{urllib.parse.urlencode({'tenantId': tenant_id})}"
    request = urllib.request.Request(
        url,
        headers={
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
        },
    )

    with urllib.request.urlopen(request, timeout=30) as response:
        if response.status != 200:
            raise urllib.error.HTTPError(
                url,
                response.status,
                f"GET request failed with status {response.status}",
                response.headers,
                None,
            )
        response_data = json.loads(response.read().decode())

    # Parse existing instance names
    existing_instances = response_data.get("data", [])
    existing_names = {instance["name"] for instance in existing_instances}

    # Determine what needs to be created
    desired_names = {customer["database_name"] for customer in neo4j_customers}
    to_create = list(desired_names - existing_names)

    # Generate hash for change detection
    to_create_json = json.dumps(sorted(to_create))
    plan_hash = hashlib.sha256(to_create_json.encode()).hexdigest()[:16]

    return {"to_create_json": to_create_json, "to_create_hash": plan_hash}


def do_apply(inputs) -> dict:
    """Create the specified Neo4j instances."""
    api_base_url = inputs["api_base_url"]
    access_token = inputs["access_token"]
    tenant_id = inputs["tenant_id"]
    to_create = inputs["to_create"]
    results_path = inputs["results_path"]

    # Instance configuration
    config = {
        "version": inputs["neo4j_version"],
        "region": inputs["aura_region"],
        "memory": inputs["instance_memory"],
        "storage": inputs["instance_storage"],
        "cloud_provider": "gcp",
        "type": inputs["aura_type"],
        "tenant_id": tenant_id,
    }

    created_instances = {}

    # Create each instance
    for database_name in to_create:
        instance_data = {**config, "name": database_name}

        request_data = json.dumps(instance_data).encode()
        request = urllib.request.Request(
            f"{api_base_url}/instances",
            data=request_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
        )

        with urllib.request.urlopen(request, timeout=60) as response:
            if response.status not in (200, 201):
                raise urllib.error.HTTPError(
                    response.url,
                    response.status,
                    f"POST request failed with status {response.status}",
                    response.headers,
                    None,
                )
            response_data = json.loads(response.read().decode())

        # Extract credentials from response
        data = response_data.get("data", {})
        created_instances[database_name] = {
            "instance_id": data.get("id"),
            "database_name": database_name,
            "username": data.get("username"),
            "password": data.get("password"),
        }

    # Save results to file
    with open(results_path, "w", encoding="utf-8") as f:
        json.dump(created_instances, f, indent=2)

    return {"status": "ok", "created_count": str(len(created_instances))}


def do_read_created(inputs):
    """Read previously created instance results from file."""
    results_path = inputs.get("results_path", "/tmp/neo4j_created.json")

    if os.path.exists(results_path):
        with open(results_path, "r", encoding="utf-8") as f:
            created_map = json.load(f)
    else:
        created_map = {}

    return {"created_map_json": json.dumps(created_map)}


def main():
    """Main entry point for the script."""
    try:
        inputs = json.load(sys.stdin)
        action = inputs.get("action", "plan")

        if action == "plan":
            result = do_plan(inputs)
        elif action == "apply":
            result = do_apply(inputs)
        elif action == "read_created":
            result = do_read_created(inputs)
        else:
            raise ValueError(f"Unknown action: {action}")

        json.dump(result, sys.stdout)

    except urllib.error.HTTPError as e:
        json.dump({"error": f"HTTP request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except urllib.error.URLError as e:
        json.dump({"error": f"API request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except json.JSONDecodeError as e:
        json.dump({"error": f"JSON parsing failed: {e}"}, sys.stdout)
        sys.exit(1)
    except ValueError as e:
        json.dump({"error": f"Invalid input: {e}"}, sys.stdout)
        sys.exit(1)
    except OSError as e:
        json.dump({"error": f"File operation failed: {e}"}, sys.stdout)
        sys.exit(1)
    except KeyError as e:
        json.dump({"error": f"Missing required input: {e}"}, sys.stdout)
        sys.exit(1)


if __name__ == "__main__":
    main()
